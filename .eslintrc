env:
  commonjs: true
  es2021: true
  node: true
  mocha: true
globals:
  Recipe: true
  helper: true
  athenaService: true
  Alert: true
  Diagnosticrecipe: true
  MaintenanceMode: true
  MaintenanceModeLookUp: true
  sails: true
  jwtService: true
  Users: true
  Version: true
  cacheService: true
  eventService: true
  UserSiteMap: true
  DyanmoKeyStore: true
  Baseline: true
  baselineService: true
  DailyConsumption: true
  diagnosticService: true
  Command: true
  commandService: true
  Sites: true
  siteService: true
  Component: true
  componentService: true
  DataDevice: true
  dataDeviceService: true
  Devices: true
  dashboardService: true
  deviceService: true
  DeviceType: true
  Datadevice: true
  dailyConsumptionService: true
  Mode: true
  dbHelper: true
  RecipeService: true
  modeService: true
  recipeService: true
  alertService: true
  Schedules: true
  IFTTT_helper: true
  Repaired: true
  Unrepaired: true
  Role: true
  repairedService: true
  userService: true
  sendsms: true
  snsHelper: true
  sqsService: true
  ChangeLog: true
  broadcastService: true
  parameterService: true
  parametersService: true
  Parameters: true
  Actions: true
  feedbackService: true
  deviceTypeService: true
  versionUtil: true
  diagnosticUtil: true
  Reportinfo: true
  sendmail: true
  Processes: true
  processService: true
  Production: true
  productionService: true
  Queriedparameters: true
  timeSeries: true
extends:
  - airbnb-base
parserOptions:
  ecmaVersion: 12
rules: {}
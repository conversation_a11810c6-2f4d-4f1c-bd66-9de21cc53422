################################################
###############  .gitignore   ##################
################################################
#
# This file is only relevant if you are using git.
#
# Files which match the splat patterns below will
# be ignored by git.  This keeps random crap and
# sensitive credentials from being uploaded to
# your repository.  It allows you to configure your
# app for your machine without accidentally
# committing settings which will smash the local
# settings of  other developers on your team.
#
# Some reasonable defaults are included below,
# but, of course, you should modify/extend/prune
# to fit your needs!
################################################




################################################
# Local Configuration
#
# Explicitly ignore files which contain:
#
# 1. Sensitive information you'd rather not push to
#    your git repository.
#    e.g., your personal API keys or passwords.
#
# 2. Environment-specific configuration
#    Basically, anything that would be annoying
#    to have to change every time you do a
#    `git pull`
#    e.g., your local development database, or
#    the S3 bucket you're using for file uploads
#    development.
#
################################################

config/local.js
testresult




################################################
# Dependencies
#
# When releasing a production app, you may
# consider including your node_modules and
# bower_components directory in your git repo,
# but during development, its best to exclude it,
# since different developers may be working on
# different kernels, where dependencies would
# need to be recompiled anyway.
#
# More on that here about node_modules dir:
# http://www.futurealoof.com/posts/nodemodules-in-git.html
# (credit Mikeal Rogers, @mikeal)
#
# About bower_components dir, you can see this:
# http://addyosmani.com/blog/checking-in-front-end-dependencies/
# (credit Addy Osmani, @addyosmani)
#
################################################

node_modules
bower_components
config/env/keys.js
config/env/keys.json

################################################
# Sails.js / Waterline / Grunt
#
# Files generated by Sails and Grunt, or related
# tasks and adapters.
################################################
.tmp
dump.rdb





################################################
# Node.js / NPM
#
# Common files generated by Node, NPM, and the
# related ecosystem.
################################################
lib-cov
*.seed
*.log
*.out
*.pid
npm-debug.log
package-lock.json





################################################
# Miscellaneous
#
# Common files generated by text editors,
# operating systems, file systems, etc.
################################################

*~
*#
.DS_STORE
.netbeans
nbproject
.idea
.node_history

# Local Dev Environment Files
*.db
*.rdb
nodemon.json
# file generated to help populate mock data.
testData.json

out/
out/*

.nyc*
.nyc/
.nyc/*
coverage/
coverage/*
amantest/

mockdata/*
keys/kubernetes/token

.vscode/*

# Sonar Qube
.scannerwork
.env
